/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #41535D 0%, #D9DDDF 100%);
    min-height: 100vh;
    color: #1e293b;
    line-height: 1.6;
}

/* Header Styles */
.header {
    background-color: #41535D;
    color: white;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.logo-image {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

.logo-fallback {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.025em;
    color: #ffffff;
}

.header-title h1 {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.settings-toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
}

.settings-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(180deg);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-name {
    font-weight: 500;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Container */
.main-container {
    display: flex;
    margin-top: 64px;
    min-height: calc(100vh - 64px);
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background-color: #D9DDDF;
    border-right: 1px solid rgba(65, 83, 93, 0.2);
    transition: all 0.3s ease;
    position: fixed;
    left: 0;
    top: 64px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    z-index: 100;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-bottom: 1px solid rgba(65, 83, 93, 0.2);
    background-color: rgba(65, 83, 93, 0.05);
}

.sidebar-header .menu-toggle {
    background: none;
    border: none;
    color: #41535D;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;
}

.sidebar-header .menu-toggle:hover {
    background-color: rgba(65, 83, 93, 0.1);
}

.nav-title {
    font-weight: 600;
    color: #41535D;
    font-size: 16px;
    transition: all 0.3s ease;
}

.sidebar.collapsed .nav-title {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #41535D;
    text-decoration: none;
    border-radius: 0;
    transition: all 0.3s;
    font-weight: 500;
    position: relative;
    border-bottom: 1px solid rgba(65, 83, 93, 0.1);
    white-space: nowrap;
}

.nav-link:hover {
    background-color: rgba(65, 83, 93, 0.1);
    color: #41535D;
}

.nav-item.active .nav-link {
    background-color: rgba(65, 83, 93, 0.2);
    color: #41535D;
    font-weight: 600;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-link {
    padding: 12px 16px;
    justify-content: center;
}

.nav-link span {
    transition: all 0.3s ease;
    overflow: hidden;
}

/* Right Sidebar Styles */
.right-sidebar {
    width: 280px;
    background-color: #D9DDDF;
    border-left: 1px solid rgba(65, 83, 93, 0.2);
    transition: all 0.3s ease;
    position: fixed;
    right: 0;
    top: 64px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    z-index: 100;
}

.right-sidebar.collapsed {
    width: 70px;
}

.right-sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.right-sidebar.collapsed .nav-link {
    padding: 12px 16px;
    justify-content: center;
}

.right-sidebar.collapsed .nav-title {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    margin-right: 0;
    padding: 24px;
    transition: all 0.3s ease;
    min-height: calc(100vh - 64px);
    background: transparent;
}

.main-content.collapsed {
    margin-left: 70px;
}

.main-content.right-sidebar-active {
    margin-left: 0;
    margin-right: 280px;
}

.main-content.right-sidebar-active.collapsed {
    margin-right: 70px;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-title h2 {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.dashboard-title p {
    color: #64748b;
    font-size: 16px;
}

.dashboard-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.btn-primary {
    background-color: #41535D;
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(65, 83, 93, 0.4);
    background-color: #2d3a42;
}

.btn-secondary {
    background-color: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background-color: #e2e8f0;
    transform: translateY(-1px);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.accept {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.hold {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.not-applicable {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.stat-icon.pending {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-icon.rejected {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-content p {
    color: #64748b;
    font-size: 14px;
}

/* Controls Section */
.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 16px;
}

.filters {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: #64748b;
    font-size: 14px;
}

.search-box input {
    padding: 10px 12px 10px 36px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: #41535D;
}

.filter-select {
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: #41535D;
}

.view-controls {
    display: flex;
    gap: 4px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 8px;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: #64748b;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s;
    font-size: 16px;
}

.view-btn.active,
.view-btn:hover {
    background: white;
    color: #41535D;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Content Area */
.content-area {
    margin-bottom: 32px;
}

.grid-view,
.card-view,
.list-view {
    display: none;
}

.grid-view.active,
.card-view.active,
.list-view.active {
    display: block;
}

/* Grid View */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.grid-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    border-left: 4px solid transparent;
}

.grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.grid-item.accept {
    border-left-color: #10b981;
}

.grid-item.hold {
    border-left-color: #f59e0b;
}

.grid-item.not-applicable {
    border-left-color: #6b7280;
}

.grid-item.pending {
    border-left-color: #3b82f6;
}

.grid-item.rejected {
    border-left-color: #ef4444;
}

.grid-item-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.grid-item-id {
    font-size: 12px;
    color: #64748b;
    background: #f1f5f9;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.grid-item-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-accept {
    background: #dcfce7;
    color: #166534;
}

.status-hold {
    background: #fef3c7;
    color: #92400e;
}

.status-not-applicable {
    background: #f3f4f6;
    color: #374151;
}

.status-pending {
    background: #dbeafe;
    color: #1e40af;
}

.status-rejected {
    background: #fee2e2;
    color: #991b1b;
}

.grid-item-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.4;
}

.grid-item-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 13px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    color: #64748b;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.meta-value {
    color: #1e293b;
    font-weight: 500;
}

.grid-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #f1f5f9;
}

.pending-days {
    font-size: 12px;
    color: #64748b;
}

.pending-days.high {
    color: #ef4444;
    font-weight: 600;
}

.timeline-badge {
    background: #f1f5f9;
    color: #64748b;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

/* Card View */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
}

.card-item {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.card-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #059669);
}

.card-item.hold::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.card-item.not-applicable::before {
    background: linear-gradient(90deg, #6b7280, #4b5563);
}

.card-item.pending::before {
    background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.card-item.rejected::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.card-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.card-id {
    font-size: 14px;
    color: #64748b;
    background: #f8fafc;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
}

.card-status {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
    line-height: 1.4;
}

.card-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 16px;
}

.detail-group {
    display: flex;
    flex-direction: column;
}

.detail-label {
    color: #64748b;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
    font-weight: 500;
}

.detail-value {
    color: #1e293b;
    font-weight: 600;
    font-size: 14px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f1f5f9;
}

.pending-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.pending-icon {
    color: #64748b;
}

.pending-text {
    color: #64748b;
}

.pending-text.high {
    color: #ef4444;
    font-weight: 600;
}

/* List View */
.list-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.list-header {
    background: #f8fafc;
    padding: 16px 24px;
    border-bottom: 1px solid #e2e8f0;
    display: grid;
    grid-template-columns: 120px 2fr 1fr 100px 100px 80px 100px 80px;
    gap: 16px;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.list-item {
    padding: 16px 24px;
    border-bottom: 1px solid #f1f5f9;
    display: grid;
    grid-template-columns: 120px 2fr 1fr 100px 100px 80px 100px 80px;
    gap: 16px;
    align-items: center;
    transition: background-color 0.2s;
    position: relative;
}

.list-item:hover {
    background: #f8fafc;
}

.list-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #10b981;
}

.list-item.hold::before {
    background: #f59e0b;
}

.list-item.not-applicable::before {
    background: #6b7280;
}

.list-item.pending::before {
    background: #3b82f6;
}

.list-item.rejected::before {
    background: #ef4444;
}

.list-id {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
}

.list-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.list-initiator {
    color: #64748b;
    font-size: 13px;
}

.list-date {
    color: #64748b;
    font-size: 12px;
}

.list-tabs {
    font-size: 12px;
    color: #64748b;
}

.list-pending {
    font-size: 12px;
    color: #64748b;
}

.list-pending.high {
    color: #ef4444;
    font-weight: 600;
}

.list-timeline {
    font-size: 11px;
    color: #64748b;
}

.list-os {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
}

.list-os.hold {
    background: #f59e0b;
}

.list-os.not-applicable {
    background: #6b7280;
}

.list-os.pending {
    background: #3b82f6;
}

.list-os.rejected {
    background: #ef4444;
}

/* Pagination */
.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pagination-info {
    color: #64748b;
    font-size: 14px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #64748b;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #41535D;
    color: #41535D;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-number {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #64748b;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.page-number.active {
    background: #41535D;
    color: white;
    border-color: #41535D;
}

.page-number:hover:not(.active) {
    background: #f8fafc;
    border-color: #41535D;
    color: #41535D;
}

.pagination-ellipsis {
    padding: 8px 4px;
    color: #64748b;
}

/* Legend Modal */
.legend-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.legend-modal.show {
    display: flex;
}

.legend-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.legend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.legend-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.legend-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
}

.legend-color.accept {
    background: #10b981;
}

.legend-color.hold {
    background: #f59e0b;
}

.legend-color.not-applicable {
    background: #6b7280;
}

.legend-color.pending {
    background: #3b82f6;
}

.legend-color.rejected {
    background: #ef4444;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        width: 280px;
    }

    .main-content {
        margin-left: 0;
    }

    .main-content.collapsed {
        margin-left: 0;
    }

    .right-sidebar {
        transform: translateX(100%);
        width: 280px;
    }

    .right-sidebar.show {
        transform: translateX(0);
    }

    .right-sidebar.collapsed {
        width: 280px;
    }

    .main-content.right-sidebar-active {
        margin-right: 0;
    }

    .main-content.right-sidebar-active.collapsed {
        margin-right: 0;
    }

    .controls-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filters {
        justify-content: center;
    }

    .view-controls {
        align-self: center;
    }

    .grid-container {
        grid-template-columns: 1fr;
    }

    .card-container {
        grid-template-columns: 1fr;
    }

    .list-header,
    .list-item {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .list-header {
        display: none;
    }

    .list-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0 16px;
    }

    .header-title h1 {
        display: none;
    }

    .main-content {
        padding: 16px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .dashboard-actions {
        justify-content: flex-start;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .search-box input {
        width: 100%;
    }

    .pagination-section {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .pagination {
        justify-content: center;
    }
}
