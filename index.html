<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIRTGEN - CDP Idea Tracking System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo">
                <span class="logo-icon">W</span>
                <span class="logo-text">WIRTGEN</span>
            </div>
            <div class="header-title">
                <h1>CDP Idea Tracking System - 'User Management'</h1>
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <span class="user-name">Shaiva Ravindra</span>
                <button class="logout-btn">Log Out</button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-briefcase"></i>
                            <span>Work Load</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea Hopper</span>
                        </a>
                    </li>
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea</span>
                           
                        </a>
                    </li>
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-check-circle"></i>
                            <span>Price Approval</span>
                            
                        </a>
                    </li>
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                    </li>
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-sync-alt"></i>
                            <span>SAP Update Workload</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-circle"></i>
                                    <span>Pending for Updation</span>
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-circle"></i>
                                    <span>SAP Completed</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Workload Transfer</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-calculator"></i>
                            <span>Cost Estimation & Budgeting</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="#" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Content Header -->
            <div class="content-header">
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="employee">Employee</button>
                    <button class="tab-btn" data-tab="role-master">Role Master</button>
                </div>
            </div>

            <!-- Employee Tab Content -->
            <div class="tab-content active" id="employee-tab">
                <div class="table-container">
                    <div class="table-header">
                        <h2>Employee</h2>
                        <div class="table-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add Employee
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>

                    <div class="table-filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search employees..." id="searchInput">
                        </div>
                        <div class="filter-controls">
                            <select class="filter-select" id="departmentFilter">
                                <option value="">All Departments</option>
                                <option value="Design">Design</option>
                                <option value="Sourcing">Sourcing</option>
                                <option value="Engineering">Engineering</option>
                                <option value="Quality">Quality</option>
                            </select>
                            <select class="filter-select" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="Yes">Active</option>
                                <option value="No">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table class="data-table" id="employeeTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-column="0">
                                        <i class="fas fa-edit"></i>
                                        Edit
                                    </th>
                                    <th class="sortable" data-column="1">
                                        Employee Code
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-column="2">
                                        Name
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-column="3">
                                        Email
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-column="4">
                                        Department
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-column="5">
                                        Designation
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-column="6">
                                        Is Active?
                                        <i class="fas fa-sort"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="employeeTableBody">
                                <!-- Employee data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <div class="table-footer">
                        <div class="pagination-info">
                            <span>Showing <span id="showingStart">1</span> to <span id="showingEnd">15</span> of <span id="totalRecords">50</span> entries</span>
                        </div>
                        <div class="pagination">
                            <button class="pagination-btn" id="prevBtn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="pagination-numbers" id="paginationNumbers">
                                <!-- Pagination numbers will be generated by JavaScript -->
                            </div>
                            <button class="pagination-btn" id="nextBtn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Master Tab Content -->
            <div class="tab-content" id="role-master-tab">
                <div class="table-container">
                    <div class="table-header">
                        <h2>Role Master</h2>
                        <div class="table-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add Role
                            </button>
                        </div>
                    </div>
                    <p class="coming-soon">Role Master functionality coming soon...</p>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
