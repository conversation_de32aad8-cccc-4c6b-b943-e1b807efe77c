/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #41535D 0%, #D9DDDF 100%);
    color: #334155;
    line-height: 1.6;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background-color: #41535D;
    color: #ffffff;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.menu-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
}

.logo-image {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

.logo-fallback {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.025em;
    color: #ffffff;
}

.header-title h1 {
    font-size: 18px;
    font-weight: 500;
    opacity: 0.95;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-name {
    font-weight: 500;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Container */
.main-container {
    display: flex;
    margin-top: 64px;
    min-height: calc(100vh - 64px);
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background-color: #D9DDDF;
    border-right: 1px solid rgba(65, 83, 93, 0.2);
    transition: all 0.3s ease;
    position: fixed;
    left: 0;
    top: 64px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    z-index: 100;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .submenu-arrow {
    display: none;
}



.sidebar-nav {
    padding: 16px 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin: 2px 12px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #41535D;
    text-decoration: none;
    border-radius: 0;
    transition: all 0.3s;
    font-weight: 500;
    position: relative;
    border-bottom: 1px solid rgba(65, 83, 93, 0.1);
    white-space: nowrap;
}

.nav-link:hover {
    background-color: rgba(65, 83, 93, 0.1);
    color: #41535D;
}

.nav-item.active .nav-link {
    background-color: rgba(65, 83, 93, 0.2);
    color: #41535D;
    font-weight: 600;
}

.nav-link span {
    transition: all 0.3s ease;
    overflow: hidden;
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.submenu-arrow {
    margin-left: auto !important;
    width: auto !important;
    font-size: 12px !important;
    transition: transform 0.2s;
}

.has-submenu .nav-link:hover .submenu-arrow {
    transform: rotate(180deg);
}



/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 24px;
    transition: margin-left 0.3s ease;
    min-height: calc(100vh - 64px);
    background: transparent;
}

.main-content.collapsed {
    margin-left: 70px;
}

/* Content Header */
.content-header {
    margin-bottom: 24px;
}

.tab-navigation {
    display: flex;
    gap: 4px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 8px;
    width: fit-content;
}

.tab-btn {
    background: none;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    transition: all 0.2s;
}

.tab-btn.active {
    background: white;
    color: #41535D;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Table Container */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
}

.table-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.table-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #41535D;
    color: #ffffff;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(65, 83, 93, 0.4);
    background-color: #2d3a42;
}

.btn-secondary {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f1f5f9;
    color: #334155;
}

/* Table Filters */
.table-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    gap: 16px;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
}

.search-box input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.search-box input:focus {
    outline: none;
    border-color: #41535D;
    box-shadow: 0 0 0 3px rgba(65, 83, 93, 0.1);
}

.filter-controls {
    display: flex;
    gap: 12px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #41535D;
}

/* Table Styles */
.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
}

.data-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.data-table th.sortable:hover {
    background: #f1f5f9;
}

.data-table th i.fa-sort {
    margin-left: 8px;
    opacity: 0.5;
    font-size: 12px;
}

.data-table tbody tr:hover {
    background: #f8fafc;
}

.edit-btn {
    background: none;
    border: none;
    color: #41535D;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.edit-btn:hover {
    background: #f1f5f9;
    color: #41535D;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

/* Table Footer */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.pagination-info {
    color: #64748b;
    font-size: 14px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    background: white;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background: #f1f5f9;
    color: #334155;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: 4px;
}

.page-number {
    background: white;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 36px;
    text-align: center;
}

.page-number:hover {
    background: #f1f5f9;
    color: #334155;
}

.page-number.active {
    background: #41535D;
    color: white;
    border-color: #41535D;
}

.coming-soon {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        width: 280px;
    }

    .main-content {
        margin-left: 0;
    }

    .main-content.collapsed {
        margin-left: 0;
    }

    .table-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0 16px;
    }
    
    .header-title h1 {
        display: none;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .table-actions {
        justify-content: flex-start;
    }
    
    .table-footer {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .pagination {
        justify-content: center;
    }
}
