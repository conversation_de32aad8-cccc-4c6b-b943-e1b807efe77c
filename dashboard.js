// Sample data based on the provided image
const ideaData = [
    {
        id: 'PHamm-2024-11-4-Neg-798',
        title: 'Ash CAP',
        initiator: 'shaiva ravindra',
        docDate: '04/11/2024',
        tabs: 'Execution',
        pendingDays: 218,
        timeline: '2025.Q2',
        os: 'Accept/Approve',
        status: 'accept'
    },
    {
        id: 'PHamm-2024-3-22-WAVE-797',
        title: 'testVB',
        initiator: 'shaiva ravindra',
        docDate: '23/04/2024',
        tabs: 'Execution',
        pendingDays: 419,
        timeline: '2025.Q2',
        os: 'Accept/Approve',
        status: 'accept'
    },
    {
        id: 'PHamm-2024-3-22-WAVE-795',
        title: 'usermanagement_Test',
        initiator: 'shaiva ravindra',
        docDate: '20/03/2024',
        tabs: 'Execution',
        pendingDays: 445,
        timeline: '2024.Q3',
        os: 'Accept/Approve',
        status: 'accept'
    },
    {
        id: 'PHamm-2024-3-22-WAVE-796',
        title: 'TO show',
        initiator: 'shaiva ravindra',
        docDate: '22/03/2024',
        tabs: 'Execution',
        pendingDays: 445,
        timeline: '2024.Q3',
        os: 'Accept/Approve',
        status: 'accept'
    },
    {
        id: 'PHamm-2023-11-24-Neg-794',
        title: 'todaycheck',
        initiator: 'shaiva ravindra',
        docDate: '24/11/2023',
        tabs: 'Execution',
        pendingDays: 564,
        timeline: '2023.Q2',
        os: 'Hold',
        status: 'hold'
    },
    {
        id: 'PHamm-2023-11-3-Neg-793',
        title: 'swcant',
        initiator: 'shaiva ravindra',
        docDate: '03/11/2023',
        tabs: 'Evaluation',
        pendingDays: 585,
        timeline: '2023.Q2',
        os: 'Hold',
        status: 'hold'
    },
    {
        id: 'PHamm-2023-11-2-Neg-792',
        title: 'New radio button',
        initiator: 'shaiva ravindra',
        docDate: '02/11/2023',
        tabs: 'Execution',
        pendingDays: 586,
        timeline: '2023.Q3',
        os: 'Not Applicable',
        status: 'not-applicable'
    },
    {
        id: 'PHamm-2023-10-30-Neg-791',
        title: 'WLTCHECKANJP',
        initiator: 'shaiva ravindra',
        docDate: '30/10/2023',
        tabs: 'Evaluation',
        pendingDays: 589,
        timeline: '2023.Q3',
        os: 'Pending With',
        status: 'pending'
    },
    {
        id: 'PHamm-2023-10-19-Neg-790',
        title: 'New member added Check',
        initiator: 'shaiva ravindra',
        docDate: '19/10/2023',
        tabs: 'Execution',
        pendingDays: 601,
        timeline: '2023.Q2',
        os: 'Rejected',
        status: 'rejected'
    },
    {
        id: 'PHamm-2023-10-18-WAVE-789',
        title: 'Execution ENG check',
        initiator: 'shaiva ravindra',
        docDate: '18/10/2023',
        tabs: 'Evaluation',
        pendingDays: 601,
        timeline: '2024.Q3',
        os: 'Rejected',
        status: 'rejected'
    }
];

// Global variables
let filteredData = [...ideaData];
let currentPage = 1;
const itemsPerPage = 12;
let currentView = 'grid';
let sortColumn = '';
let sortDirection = 'asc';

// Menu position state
let isRightSidebarMode = false;

// DOM elements
const menuToggle = document.getElementById('menuToggle');
const sidebar = document.getElementById('sidebar');
const rightSidebar = document.getElementById('rightSidebar');
const rightMenuToggle = document.getElementById('rightMenuToggle');
const mainContent = document.querySelector('.main-content');
const settingsToggleBtn = document.getElementById('settingsToggleBtn');

// View elements
const viewButtons = document.querySelectorAll('.view-btn');
const gridView = document.getElementById('gridView');
const cardView = document.getElementById('cardView');
const listView = document.getElementById('listView');

// Filter elements
const searchInput = document.getElementById('searchInput');
const statusFilter = document.getElementById('statusFilter');
const initiatorFilter = document.getElementById('initiatorFilter');
const tabsFilter = document.getElementById('tabsFilter');

// Pagination elements
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const paginationNumbers = document.getElementById('paginationNumbers');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeDashboard();
});

function initializeDashboard() {
    // Set Dashboard as active menu item by default
    setActiveMenu('Dashboard');
    showDashboardContent();
}

// Event listeners
function initializeEventListeners() {
    // Menu toggles
    if (menuToggle) menuToggle.addEventListener('click', toggleSidebar);
    if (rightMenuToggle) rightMenuToggle.addEventListener('click', toggleRightSidebar);
    if (settingsToggleBtn) settingsToggleBtn.addEventListener('click', toggleMenuPosition);
    
    // View controls
    viewButtons.forEach(btn => {
        btn.addEventListener('click', () => switchView(btn.dataset.view));
    });
    
    // Filters
    if (searchInput) searchInput.addEventListener('input', handleSearch);
    if (statusFilter) statusFilter.addEventListener('change', handleFilter);
    if (initiatorFilter) initiatorFilter.addEventListener('change', handleFilter);
    if (tabsFilter) tabsFilter.addEventListener('change', handleFilter);
    
    // Pagination
    if (prevBtn) prevBtn.addEventListener('click', () => changePage(currentPage - 1));
    if (nextBtn) nextBtn.addEventListener('click', () => changePage(currentPage + 1));
    
    // Navigation menu items
    initializeMenuItems();
    
    // Responsive
    window.addEventListener('resize', handleResize);
    
    // Close sidebars when clicking outside on mobile
    document.addEventListener('click', handleOutsideClick);
}

// Menu functions
function toggleSidebar() {
    if (window.innerWidth <= 1024) {
        sidebar.classList.toggle('show');
    } else {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
    }
}

function toggleRightSidebar() {
    if (window.innerWidth <= 1024) {
        rightSidebar.classList.toggle('show');
    } else {
        rightSidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
    }
}

function toggleMenuPosition() {
    isRightSidebarMode = !isRightSidebarMode;
    
    if (isRightSidebarMode) {
        sidebar.style.display = 'none';
        rightSidebar.style.display = 'block';
        mainContent.classList.add('right-sidebar-active');
        mainContent.classList.remove('collapsed');
    } else {
        sidebar.style.display = 'block';
        rightSidebar.style.display = 'none';
        mainContent.classList.remove('right-sidebar-active');
    }
}

function initializeMenuItems() {
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e));
    });
    
    const rightNavLinks = document.querySelectorAll('.right-sidebar .nav-link');
    rightNavLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e));
    });
}

function handleMenuClick(e) {
    e.preventDefault();

    // Remove active class from all menu items in both sidebars
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    const menuItem = e.currentTarget.closest('.nav-item');
    menuItem.classList.add('active');

    const menuText = e.currentTarget.querySelector('span').textContent;
    const headerTitle = document.querySelector('.header-title h1');
    headerTitle.textContent = `CDP Idea Tracking System - ${menuText}`;

    // Handle different menu items
    const mainContent = document.querySelector('.main-content');

    if (menuText === 'Dashboard') {
        // Show dashboard content
        showDashboardContent();
    } else {
        // Hide dashboard content and show placeholder for other menu items
        showPlaceholderContent(menuText);
    }

    // Close mobile menu after selection
    if (window.innerWidth <= 1024) {
        sidebar.classList.remove('show');
        rightSidebar.classList.remove('show');
    }
}

function showDashboardContent() {
    const mainContent = document.querySelector('.main-content');

    // Show all dashboard elements
    const dashboardElements = [
        '.dashboard-header',
        '.stats-grid',
        '.controls-section',
        '.content-area',
        '.pagination-section'
    ];

    dashboardElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            element.style.display = '';
        }
    });

    // Hide placeholder content if it exists
    const placeholder = document.querySelector('.placeholder-content');
    if (placeholder) {
        placeholder.remove();
    }

    // Refresh the current view
    updateStats();
    renderCurrentView();
    updatePagination();
}

function showPlaceholderContent(menuText) {
    // Hide dashboard elements
    const dashboardElements = [
        '.dashboard-header',
        '.stats-grid',
        '.controls-section',
        '.content-area',
        '.pagination-section'
    ];

    dashboardElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Remove existing placeholder
    const existingPlaceholder = document.querySelector('.placeholder-content');
    if (existingPlaceholder) {
        existingPlaceholder.remove();
    }

    // Create placeholder content
    const placeholderHtml = `
        <div class="placeholder-content">
            <div class="placeholder-header">
                <div class="placeholder-icon">
                    <i class="fas ${getMenuIcon(menuText)}"></i>
                </div>
                <h2>${menuText}</h2>
                <p>This section is under development</p>
            </div>
            <div class="placeholder-body">
                <div class="placeholder-card">
                    <h3>Coming Soon</h3>
                    <p>The ${menuText} functionality will be available in the next update.</p>
                    <button class="btn btn-primary" onclick="showDashboardContent(); setActiveMenu('Dashboard');">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </button>
                </div>
            </div>
        </div>
    `;

    const mainContent = document.querySelector('.main-content');
    mainContent.insertAdjacentHTML('beforeend', placeholderHtml);
}

function getMenuIcon(menuText) {
    const iconMap = {
        'Work Load': 'fa-briefcase',
        'Dashboard': 'fa-tachometer-alt',
        'Idea Hopper': 'fa-lightbulb',
        'Idea': 'fa-lightbulb',
        'Price Approval': 'fa-check-circle',
        'Reports': 'fa-chart-bar',
        'SAP Update Workload': 'fa-sync-alt',
        'Workload Transfer': 'fa-exchange-alt',
        'Cost Estimation & Budgeting': 'fa-calculator',
        'User Management': 'fa-users'
    };

    return iconMap[menuText] || 'fa-cog';
}

function setActiveMenu(menuText) {
    // Find and activate the menu item
    const menuLinks = document.querySelectorAll('.nav-link');
    menuLinks.forEach(link => {
        const span = link.querySelector('span');
        if (span && span.textContent === menuText) {
            // Remove active from all items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active to this item
            link.closest('.nav-item').classList.add('active');

            // Update header title
            const headerTitle = document.querySelector('.header-title h1');
            headerTitle.textContent = `CDP Idea Tracking System - ${menuText}`;
        }
    });
}

// View switching
function switchView(viewType) {
    currentView = viewType;
    
    // Update view buttons
    viewButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
    
    // Update view containers
    document.querySelectorAll('.grid-view, .card-view, .list-view').forEach(view => {
        view.classList.remove('active');
    });
    
    document.getElementById(`${viewType}View`).classList.add('active');
    
    // Render current view
    renderCurrentView();
}

// Stats update
function updateStats() {
    const stats = {
        accept: ideaData.filter(item => item.status === 'accept').length,
        hold: ideaData.filter(item => item.status === 'hold').length,
        'not-applicable': ideaData.filter(item => item.status === 'not-applicable').length,
        pending: ideaData.filter(item => item.status === 'pending').length,
        rejected: ideaData.filter(item => item.status === 'rejected').length
    };
    
    document.getElementById('acceptedCount').textContent = stats.accept;
    document.getElementById('holdCount').textContent = stats.hold;
    document.getElementById('notApplicableCount').textContent = stats['not-applicable'];
    document.getElementById('pendingCount').textContent = stats.pending;
    document.getElementById('rejectedCount').textContent = stats.rejected;
}

// Search and filter
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    filteredData = ideaData.filter(item =>
        item.id.toLowerCase().includes(searchTerm) ||
        item.title.toLowerCase().includes(searchTerm) ||
        item.initiator.toLowerCase().includes(searchTerm)
    );
    
    applyFilters();
    currentPage = 1;
    renderCurrentView();
    updatePagination();
}

function handleFilter() {
    filteredData = ideaData.filter(item => {
        const statusMatch = !statusFilter.value || item.os === statusFilter.value;
        const initiatorMatch = !initiatorFilter.value || item.initiator === initiatorFilter.value;
        const tabsMatch = !tabsFilter.value || item.tabs === tabsFilter.value;
        const searchMatch = !searchInput.value || 
            item.id.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            item.title.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            item.initiator.toLowerCase().includes(searchInput.value.toLowerCase());
        
        return statusMatch && initiatorMatch && tabsMatch && searchMatch;
    });
    
    currentPage = 1;
    renderCurrentView();
    updatePagination();
}

function applyFilters() {
    const statusValue = statusFilter ? statusFilter.value : '';
    const initiatorValue = initiatorFilter ? initiatorFilter.value : '';
    const tabsValue = tabsFilter ? tabsFilter.value : '';

    if (statusValue || initiatorValue || tabsValue) {
        filteredData = filteredData.filter(item => {
            const statusMatch = !statusValue || item.os === statusValue;
            const initiatorMatch = !initiatorValue || item.initiator === initiatorValue;
            const tabsMatch = !tabsValue || item.tabs === tabsValue;
            return statusMatch && initiatorMatch && tabsMatch;
        });
    }
}

// Render functions
function renderCurrentView() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    switch (currentView) {
        case 'grid':
            renderGridView(pageData);
            break;
        case 'card':
            renderCardView(pageData);
            break;
        case 'list':
            renderListView(pageData);
            break;
    }
}

function renderGridView(data) {
    const container = document.getElementById('gridContainer');
    if (!container) return;

    container.innerHTML = data.map(item => `
        <div class="grid-item ${item.status}">
            <div class="grid-item-header">
                <span class="grid-item-id">${item.id}</span>
                <span class="grid-item-status status-${item.status}">${item.os}</span>
            </div>
            <h3 class="grid-item-title">${item.title}</h3>
            <div class="grid-item-meta">
                <div class="meta-item">
                    <span class="meta-label">Initiator</span>
                    <span class="meta-value">${item.initiator}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Doc Date</span>
                    <span class="meta-value">${item.docDate}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Tabs</span>
                    <span class="meta-value">${item.tabs}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Timeline</span>
                    <span class="meta-value">${item.timeline}</span>
                </div>
            </div>
            <div class="grid-item-footer">
                <span class="pending-days ${item.pendingDays > 500 ? 'high' : ''}">${item.pendingDays} days</span>
                <span class="timeline-badge">${item.timeline}</span>
            </div>
        </div>
    `).join('');
}

function renderCardView(data) {
    const container = document.getElementById('cardContainer');
    if (!container) return;

    container.innerHTML = data.map(item => `
        <div class="card-item ${item.status}">
            <div class="card-header">
                <span class="card-id">${item.id}</span>
                <span class="card-status status-${item.status}">${item.os}</span>
            </div>
            <h3 class="card-title">${item.title}</h3>
            <div class="card-details">
                <div class="detail-group">
                    <span class="detail-label">Initiator</span>
                    <span class="detail-value">${item.initiator}</span>
                </div>
                <div class="detail-group">
                    <span class="detail-label">Doc Date</span>
                    <span class="detail-value">${item.docDate}</span>
                </div>
                <div class="detail-group">
                    <span class="detail-label">Tabs</span>
                    <span class="detail-value">${item.tabs}</span>
                </div>
                <div class="detail-group">
                    <span class="detail-label">Timeline</span>
                    <span class="detail-value">${item.timeline}</span>
                </div>
            </div>
            <div class="card-footer">
                <div class="pending-info">
                    <i class="fas fa-clock pending-icon"></i>
                    <span class="pending-text ${item.pendingDays > 500 ? 'high' : ''}">${item.pendingDays} days pending</span>
                </div>
                <span class="timeline-badge">${item.timeline}</span>
            </div>
        </div>
    `).join('');
}

function renderListView(data) {
    const container = document.getElementById('listContainer');
    if (!container) return;

    const headerHtml = `
        <div class="list-header">
            <div>Idea ID</div>
            <div>Idea Title</div>
            <div>Idea Initiator</div>
            <div>Doc Date</div>
            <div>Tabs</div>
            <div>Pending Days</div>
            <div>Timeline</div>
            <div>OS</div>
        </div>
    `;

    const itemsHtml = data.map(item => `
        <div class="list-item ${item.status}">
            <div class="list-id">${item.id}</div>
            <div class="list-title">${item.title}</div>
            <div class="list-initiator">${item.initiator}</div>
            <div class="list-date">${item.docDate}</div>
            <div class="list-tabs">${item.tabs}</div>
            <div class="list-pending ${item.pendingDays > 500 ? 'high' : ''}">${item.pendingDays}</div>
            <div class="list-timeline">${item.timeline}</div>
            <div class="list-os ${item.status}"></div>
        </div>
    `).join('');

    container.innerHTML = headerHtml + itemsHtml;
}

// Pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredData.length);

    // Update pagination info
    document.getElementById('showingStart').textContent = filteredData.length > 0 ? startItem : 0;
    document.getElementById('showingEnd').textContent = endItem;
    document.getElementById('totalRecords').textContent = filteredData.length;

    // Update pagination buttons
    if (prevBtn) prevBtn.disabled = currentPage === 1;
    if (nextBtn) nextBtn.disabled = currentPage === totalPages || totalPages === 0;

    // Generate page numbers
    generatePageNumbers(totalPages);
}

function generatePageNumbers(totalPages) {
    if (!paginationNumbers) return;

    let html = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
        html += `<button class="page-number" onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `<button class="page-number ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
        html += `<button class="page-number" onclick="changePage(${totalPages})">${totalPages}</button>`;
    }

    paginationNumbers.innerHTML = html;
}

function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderCurrentView();
        updatePagination();

        // Scroll to top of content
        document.querySelector('.main-content').scrollTop = 0;
    }
}

// Responsive handling
function handleResize() {
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('show');
        rightSidebar.classList.remove('show');
    }
}

function handleOutsideClick(e) {
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
            sidebar.classList.remove('show');
        }
        if (!rightSidebar.contains(e.target) && !rightMenuToggle.contains(e.target)) {
            rightSidebar.classList.remove('show');
        }
    }
}

// Additional functionality
document.getElementById('refreshBtn')?.addEventListener('click', function() {
    // Reset filters
    if (searchInput) searchInput.value = '';
    if (statusFilter) statusFilter.value = '';
    if (initiatorFilter) initiatorFilter.value = '';
    if (tabsFilter) tabsFilter.value = '';

    // Reset data and view
    filteredData = [...ideaData];
    currentPage = 1;
    updateStats();
    renderCurrentView();
    updatePagination();

    // Show refresh animation
    this.style.transform = 'rotate(360deg)';
    setTimeout(() => {
        this.style.transform = 'rotate(0deg)';
    }, 500);
});

document.getElementById('addIdeaBtn')?.addEventListener('click', function() {
    alert('Add New Idea functionality would be implemented here');
});

// Legend modal functionality
const legendModal = document.getElementById('legendModal');
const closeLegend = document.getElementById('closeLegend');

// Show legend when clicking on OS column or status indicators
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('list-os') || e.target.classList.contains('stat-icon')) {
        legendModal.classList.add('show');
    }
});

closeLegend?.addEventListener('click', function() {
    legendModal.classList.remove('show');
});

legendModal?.addEventListener('click', function(e) {
    if (e.target === legendModal) {
        legendModal.classList.remove('show');
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        legendModal.classList.remove('show');
        if (window.innerWidth <= 1024) {
            sidebar.classList.remove('show');
            rightSidebar.classList.remove('show');
        }
    }

    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'f':
                e.preventDefault();
                searchInput?.focus();
                break;
            case 'r':
                e.preventDefault();
                document.getElementById('refreshBtn')?.click();
                break;
        }
    }
});
