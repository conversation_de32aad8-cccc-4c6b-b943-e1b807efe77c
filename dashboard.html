<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIRTGEN - Idea Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <img src="https://www.wirtgen-group.com/ocs/wcm/idc/groups/public/@wg/@web/documents/image/mdaw/mdk4/~edisp/wirtgen_logo_white_01_4c-001.png" 
                     alt="WIRTGEN" 
                     class="logo-image"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <span class="logo-fallback" style="display: none;">WIRTGEN</span>
            </div>
            <div class="header-title">
                <h1>CDP Idea Tracking System - Dashboard</h1>
            </div>
        </div>
        <div class="header-right">
            <div class="settings-toggle">
                <button class="settings-toggle-btn" id="settingsToggleBtn" title="Switch Menu Position">
                    <i class="fas fa-exchange-alt"></i>
                </button>
            </div>
            <div class="user-info">
                <span class="user-name">Shaiva Ravindra</span>
                <button class="logout-btn">Log Out</button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Left Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="nav-title">Navigation</span>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-briefcase"></i>
                            <span>Work Load</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea Hopper</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-check-circle"></i>
                            <span>Price Approval</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-sync-alt"></i>
                            <span>SAP Update Workload</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Workload Transfer</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-calculator"></i>
                            <span>Cost Estimation & Budgeting</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Right Sidebar (Hidden by default) -->
        <aside class="right-sidebar" id="rightSidebar" style="display: none;">
            <div class="sidebar-header">
                <button class="menu-toggle" id="rightMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="nav-title">Navigation</span>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-menu" id="rightNavMenu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-briefcase"></i>
                            <span>Work Load</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea Hopper</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lightbulb"></i>
                            <span>Idea</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-check-circle"></i>
                            <span>Price Approval</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-sync-alt"></i>
                            <span>SAP Update Workload</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Workload Transfer</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-calculator"></i>
                            <span>Cost Estimation & Budgeting</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="dashboard-title">
                    <h2>Idea Dashboard</h2>
                    <p>Track and manage all ideas across different stages</p>
                </div>
                <div class="dashboard-actions">
                    <button class="btn btn-secondary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="btn btn-primary" id="addIdeaBtn">
                        <i class="fas fa-plus"></i>
                        Add New Idea
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon accept">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="acceptedCount">0</h3>
                        <p>Accepted/Approved</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon hold">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="holdCount">0</h3>
                        <p>On Hold</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon not-applicable">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="notApplicableCount">0</h3>
                        <p>Not Applicable</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="pendingCount">0</h3>
                        <p>Pending With</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon rejected">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="rejectedCount">0</h3>
                        <p>Rejected</p>
                    </div>
                </div>
            </div>

            <!-- Filters and View Controls -->
            <div class="controls-section">
                <div class="filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search ideas..." id="searchInput">
                    </div>
                    <select class="filter-select" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="Accept/Approve">Accept/Approve</option>
                        <option value="Hold">Hold</option>
                        <option value="Not Applicable">Not Applicable</option>
                        <option value="Pending With">Pending With</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                    <select class="filter-select" id="initiatorFilter">
                        <option value="">All Initiators</option>
                        <option value="shaiva ravindra">Shaiva Ravindra</option>
                    </select>
                    <select class="filter-select" id="tabsFilter">
                        <option value="">All Tabs</option>
                        <option value="Execution">Execution</option>
                        <option value="Evaluation">Evaluation</option>
                    </select>
                </div>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="card" title="Card View">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Grid View -->
                <div class="grid-view active" id="gridView">
                    <div class="grid-container" id="gridContainer">
                        <!-- Grid items will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Card View -->
                <div class="card-view" id="cardView">
                    <div class="card-container" id="cardContainer">
                        <!-- Card items will be populated by JavaScript -->
                    </div>
                </div>

                <!-- List View -->
                <div class="list-view" id="listView">
                    <div class="list-container" id="listContainer">
                        <!-- List items will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span>Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="totalRecords">0</span> entries</span>
                </div>
                <div class="pagination">
                    <button class="pagination-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <!-- Pagination numbers will be generated by JavaScript -->
                    </div>
                    <button class="pagination-btn" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Legend Modal -->
    <div class="legend-modal" id="legendModal">
        <div class="legend-content">
            <div class="legend-header">
                <h3>Status Legend</h3>
                <button class="close-btn" id="closeLegend">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="legend-body">
                <div class="legend-item">
                    <span class="legend-color accept"></span>
                    <span>OS = Operation Sourcing</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color hold"></span>
                    <span>OE = Operation Engineering</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color not-applicable"></span>
                    <span>OQ = Operation Quality</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color pending"></span>
                    <span>MS = Manager Sourcing</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color rejected"></span>
                    <span>ME = Manager Engineering</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color accept"></span>
                    <span>MQ = Manager Quality</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color hold"></span>
                    <span>HOD Sourcing = Head of Department Sourcing</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color not-applicable"></span>
                    <span>HOD Engineering = Head of Department Engineering</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color pending"></span>
                    <span>HOD Quality = Head of Department Quality</span>
                </div>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
