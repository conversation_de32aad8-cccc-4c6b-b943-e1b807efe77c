


// DOM elements
const menuToggle = document.getElementById('menuToggle');
const sidebar = document.getElementById('sidebar');
// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    renderTable();
    updatePagination();
});

// Event listeners
function initializeEventListeners() {
    // Menu toggle
    menuToggle.addEventListener('click', toggleSidebar);
    
}

// Sidebar toggle
function toggleSidebar() {
    if (window.innerWidth <= 1024) {
        sidebar.classList.toggle('show');
    } else {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    }
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
            sidebar.classList.remove('show');
        }
    }
});
