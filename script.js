// Sample employee data - empty for now
const employeeData = [];

// Global variables
let currentPage = 1;
let itemsPerPage = 15;
let filteredData = [...employeeData];
let sortColumn = null;
let sortDirection = 'asc';

// DOM elements
const menuToggle = document.getElementById('menuToggle');
const sidebar = document.getElementById('sidebar');


// Settings elements
const settingsToggleBtn = document.getElementById('settingsToggleBtn');
const rightSidebar = document.getElementById('rightSidebar');
const rightMenuToggle = document.getElementById('rightMenuToggle');
const rightNavMenu = document.getElementById('rightNavMenu');

// Menu position state
let isRightSidebarMode = false;

// Current active menu item
let activeMenuItem = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    renderTable();
    updatePagination();
});

// Event listeners
function initializeEventListeners() {
    // Menu toggle
    menuToggle.addEventListener('click', toggleSidebar);

    // Settings toggle
    settingsToggleBtn.addEventListener('click', toggleMenuPosition);

    // Right menu toggle
    if (rightMenuToggle) {
        rightMenuToggle.addEventListener('click', toggleRightSidebar);
    }

    // Initialize menu items
    initializeMenuItems();

    // Tab navigation
    tabButtons.forEach(button => {
        button.addEventListener('click', () => switchTab(button.dataset.tab));
    });

    // Search and filters
   
}

// Settings functions
function toggleMenuPosition() {
    isRightSidebarMode = !isRightSidebarMode;

    if (isRightSidebarMode) {
        // Switch to right sidebar
        sidebar.style.display = 'none';
        rightSidebar.style.display = 'block';
        mainContent.classList.add('right-sidebar-active');
        mainContent.classList.remove('collapsed');
    } else {
        // Switch to left sidebar
        sidebar.style.display = 'block';
        rightSidebar.style.display = 'none';
        mainContent.classList.remove('right-sidebar-active');
    }
}

function toggleRightSidebar() {
    if (window.innerWidth <= 1024) {
        rightSidebar.classList.toggle('show');
    } else {
        rightSidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
    }
}

// Initialize menu items
function initializeMenuItems() {
    // Sidebar menu items
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e, 'sidebar'));
    });

    // Right sidebar menu items
    const rightNavLinks = document.querySelectorAll('.right-sidebar .nav-link');
    rightNavLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e, 'rightsidebar'));
    });
}

function handleMenuClick(e, menuType) {
    e.preventDefault();

    // Remove active class from all menu items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to clicked item
    const menuItem = e.currentTarget.closest('.nav-item');
    menuItem.classList.add('active');

    // Store active menu item
    activeMenuItem = {
        text: e.currentTarget.querySelector('span').textContent,
        type: menuType
    };

    // Update page title
    const headerTitle = document.querySelector('.header-title h1');
    headerTitle.textContent = `CDP Idea Tracking System - '${activeMenuItem.text}'`;

    console.log('Selected menu:', activeMenuItem.text);
}

// Sidebar toggle
function toggleSidebar() {
    if (window.innerWidth <= 1024) {
        sidebar.classList.toggle('show');
    } else {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
    }
}

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    tabButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    tabContents.forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Search functionality

// Handle window resize
function handleResize() {
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('show');
        if (rightSidebar) {
            rightSidebar.classList.remove('show');
        }
    }
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
            sidebar.classList.remove('show');
        }
        if (rightSidebar && !rightSidebar.contains(event.target) &&
            (!rightMenuToggle || !rightMenuToggle.contains(event.target))) {
            rightSidebar.classList.remove('show');
        }
    }
});
