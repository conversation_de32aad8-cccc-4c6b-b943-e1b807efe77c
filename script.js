// Sample employee data - empty for now
const employeeData = [];

// Global variables
let currentPage = 1;
let itemsPerPage = 15;
let filteredData = [...employeeData];
let sortColumn = null;
let sortDirection = 'asc';

// DOM elements
const menuToggle = document.getElementById('menuToggle');
const sidebar = document.getElementById('sidebar');
const mainContent = document.querySelector('.main-content');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const searchInput = document.getElementById('searchInput');
const departmentFilter = document.getElementById('departmentFilter');
const statusFilter = document.getElementById('statusFilter');
const employeeTableBody = document.getElementById('employeeTableBody');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const paginationNumbers = document.getElementById('paginationNumbers');

// Settings elements
const settingsToggleBtn = document.getElementById('settingsToggleBtn');
const topNav = document.getElementById('topNav');
const topMenuToggle = document.getElementById('topMenuToggle');
const topNavMenu = document.getElementById('topNavMenu');

// Menu position state
let isTopNavMode = false;

// Current active menu item
let activeMenuItem = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    renderTable();
    updatePagination();
});

// Event listeners
function initializeEventListeners() {
    // Menu toggle
    menuToggle.addEventListener('click', toggleSidebar);

    // Settings toggle
    settingsToggleBtn.addEventListener('click', toggleMenuPosition);

    // Top menu toggle
    if (topMenuToggle) {
        topMenuToggle.addEventListener('click', toggleTopNav);
    }

    // Initialize menu items
    initializeMenuItems();

    // Tab navigation
    tabButtons.forEach(button => {
        button.addEventListener('click', () => switchTab(button.dataset.tab));
    });

    // Search and filters
    if (searchInput) searchInput.addEventListener('input', handleSearch);
    if (departmentFilter) departmentFilter.addEventListener('change', handleFilter);
    if (statusFilter) statusFilter.addEventListener('change', handleFilter);

    // Table sorting
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => handleSort(header.dataset.column));
    });

    // Pagination
    if (prevBtn) prevBtn.addEventListener('click', () => changePage(currentPage - 1));
    if (nextBtn) nextBtn.addEventListener('click', () => changePage(currentPage + 1));

    // Responsive sidebar
    window.addEventListener('resize', handleResize);
}

// Settings functions
function toggleMenuPosition() {
    isTopNavMode = !isTopNavMode;

    if (isTopNavMode) {
        // Switch to top navigation
        sidebar.style.display = 'none';
        topNav.style.display = 'block';
        mainContent.classList.add('top-nav-active');
        mainContent.classList.remove('collapsed');
    } else {
        // Switch to left sidebar
        sidebar.style.display = 'block';
        topNav.style.display = 'none';
        mainContent.classList.remove('top-nav-active');
    }
}

function toggleTopNav() {
    topNavMenu.classList.toggle('collapsed');
}

// Initialize menu items
function initializeMenuItems() {
    // Sidebar menu items
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e, 'sidebar'));
    });

    // Top nav menu items
    const topNavLinks = document.querySelectorAll('.top-nav .top-nav-link');
    topNavLinks.forEach(link => {
        link.addEventListener('click', (e) => handleMenuClick(e, 'topnav'));
    });
}

function handleMenuClick(e, menuType) {
    e.preventDefault();

    // Remove active class from all menu items
    document.querySelectorAll('.nav-item, .top-nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to clicked item
    const menuItem = e.currentTarget.closest('.nav-item, .top-nav-item');
    menuItem.classList.add('active');

    // Store active menu item
    activeMenuItem = {
        text: e.currentTarget.querySelector('span').textContent,
        type: menuType
    };

    // Update page title
    const headerTitle = document.querySelector('.header-title h1');
    headerTitle.textContent = `CDP Idea Tracking System - '${activeMenuItem.text}'`;

    console.log('Selected menu:', activeMenuItem.text);
}

// Sidebar toggle
function toggleSidebar() {
    if (window.innerWidth <= 1024) {
        sidebar.classList.toggle('show');
    } else {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('collapsed');
    }
}

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    tabButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    tabContents.forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Search functionality
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    filteredData = employeeData.filter(employee =>
        employee.name.toLowerCase().includes(searchTerm) ||
        employee.code.toLowerCase().includes(searchTerm) ||
        employee.email.toLowerCase().includes(searchTerm) ||
        employee.department.toLowerCase().includes(searchTerm) ||
        employee.designation.toLowerCase().includes(searchTerm)
    );

    applyFilters();
    currentPage = 1;
    renderTable();
    updatePagination();
}

// Filter functionality
function handleFilter() {
    const departmentValue = departmentFilter.value;
    const statusValue = statusFilter.value;

    filteredData = employeeData.filter(employee => {
        const matchesDepartment = !departmentValue || employee.department === departmentValue;
        const matchesStatus = !statusValue || employee.active === statusValue;
        const matchesSearch = !searchInput.value ||
            employee.name.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            employee.code.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            employee.email.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            employee.department.toLowerCase().includes(searchInput.value.toLowerCase()) ||
            employee.designation.toLowerCase().includes(searchInput.value.toLowerCase());

        return matchesDepartment && matchesStatus && matchesSearch;
    });

    currentPage = 1;
    renderTable();
    updatePagination();
}

// Apply current filters
function applyFilters() {
    const departmentValue = departmentFilter ? departmentFilter.value : '';
    const statusValue = statusFilter ? statusFilter.value : '';

    if (departmentValue || statusValue) {
        filteredData = filteredData.filter(employee => {
            const matchesDepartment = !departmentValue || employee.department === departmentValue;
            const matchesStatus = !statusValue || employee.active === statusValue;
            return matchesDepartment && matchesStatus;
        });
    }
}

// Sorting functionality
function handleSort(columnIndex) {
    const columns = ['', 'code', 'name', 'email', 'department', 'designation', 'active'];
    const column = columns[columnIndex];

    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    filteredData.sort((a, b) => {
        let aValue = a[column];
        let bValue = b[column];

        if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }

        if (sortDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    renderTable();
    updateSortIcons(columnIndex);
}

// Update sort icons
function updateSortIcons(activeColumn) {
    document.querySelectorAll('.sortable i.fa-sort').forEach((icon, index) => {
        if (index === parseInt(activeColumn)) {
            icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        } else {
            icon.className = 'fas fa-sort';
        }
    });
}

// Render table
function renderTable() {
    if (!employeeTableBody) return;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    if (pageData.length === 0) {
        employeeTableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: #64748b;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    No employee data available
                </td>
            </tr>
        `;
    } else {
        employeeTableBody.innerHTML = pageData.map(employee => `
            <tr>
                <td>
                    <button class="edit-btn" onclick="editEmployee(${employee.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
                <td>${employee.code}</td>
                <td>${employee.name}</td>
                <td>${employee.email}</td>
                <td>${employee.department}</td>
                <td>${employee.designation}</td>
                <td>
                    <span class="status-badge ${employee.active === 'Yes' ? 'status-active' : 'status-inactive'}">
                        ${employee.active}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    // Update showing info
    const showingStart = filteredData.length === 0 ? 0 : startIndex + 1;
    const showingEnd = Math.min(endIndex, filteredData.length);
    if (document.getElementById('showingStart')) document.getElementById('showingStart').textContent = showingStart;
    if (document.getElementById('showingEnd')) document.getElementById('showingEnd').textContent = showingEnd;
    if (document.getElementById('totalRecords')) document.getElementById('totalRecords').textContent = filteredData.length;
}

// Pagination
function updatePagination() {
    if (!paginationNumbers) return;

    const totalPages = Math.ceil(filteredData.length / itemsPerPage);

    // Update prev/next buttons
    if (prevBtn) prevBtn.disabled = currentPage === 1;
    if (nextBtn) nextBtn.disabled = currentPage === totalPages || totalPages === 0;

    // Generate page numbers
    paginationNumbers.innerHTML = '';

    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => changePage(i));
            paginationNumbers.appendChild(pageBtn);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationNumbers.appendChild(ellipsis);
        }
    }
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderTable();
        updatePagination();
    }
}

// Edit employee function
function editEmployee(id) {
    const employee = employeeData.find(emp => emp.id === id);
    if (employee) {
        alert(`Edit employee: ${employee.name} (${employee.code})`);
        // Here you would typically open a modal or navigate to an edit form
    }
}

// Handle window resize
function handleResize() {
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('show');
    }
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
            sidebar.classList.remove('show');
        }
    }
});
